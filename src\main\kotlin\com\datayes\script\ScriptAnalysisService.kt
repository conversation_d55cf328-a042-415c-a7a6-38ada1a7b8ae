package com.datayes.script

import com.datayes.hdfs.HdfsShellScriptLineageConverter
import com.datayes.lineage.*
import com.datayes.shell.ShellScriptParser
import com.datayes.sql.ColumnMapping
import com.datayes.sql.DataModificationResult
import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import com.datayes.storage.S3StorageService
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import com.datayes.sql.ColumnReference as SqlColumnReference
import com.datayes.sql.TableReference as SqlTableReference

/**
 * 脚本分析服务 (Script Analysis Service)
 *
 * UC-3: 实现脚本影响分析的核心逻辑
 */
@Service
class ScriptAnalysisService(
    private val scriptRepository: ScriptRepository,
    private val objectMapper: ObjectMapper,
    private val s3StorageService: S3StorageService,
    private val jdbcTemplate: JdbcTemplate
) {

    private val logger = LoggerFactory.getLogger(ScriptAnalysisService::class.java)

    /**
     * 异步触发脚本分析 (Trigger script analysis asynchronously)
     *
     * 在脚本上传成功后调用，异步处理分析任务
     */
    @Async
    fun triggerScriptAnalysis(scriptId: Long) {
        logger.info("3a7f8b2e | 开始异步脚本分析: scriptId=$scriptId")
        analyzeScript(scriptId)
    }

    /**
     * 分析脚本并更新结果 (Analyze script and update results)
     *
     * UC-3: 核心分析逻辑
     */
    fun analyzeScript(scriptId: Long): ScriptAnalysisResult {
        logger.info("b9d4c2f1 | 开始分析脚本: scriptId=$scriptId")

        try {
            // 1. 获取脚本信息
            val script = scriptRepository.findById(scriptId)
            if (script == null) {
                logger.error("f2e8a5c7 | 脚本不存在: scriptId=$scriptId")
                return ScriptAnalysisResult(
                    success = false,
                    message = "脚本不存在 (Script not found)",
                    scriptId = scriptId
                )
            }

            // 2. 更新状态为分析中
            val statusUpdated = scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.ANALYZING)
            if (!statusUpdated) {
                logger.error("e6f9b3a8 | 更新脚本状态失败: scriptId=$scriptId")
                return ScriptAnalysisResult(
                    success = false,
                    message = "更新脚本状态失败 (Failed to update script status)",
                    scriptId = scriptId
                )
            }

            // 3. 转换脚本为血缘信息
            val lineageResult = convertScriptToLineage(script)

            if (!lineageResult.success) {
                logger.error("h9j2k5l8 | 脚本血缘转换失败: scriptId=$scriptId, errors=${lineageResult.errors}")
                scriptRepository.updateAnalysisStatus(
                    scriptId,
                    AnalysisStatus.FAILED,
                    "血缘转换失败: ${lineageResult.errors.joinToString("; ")}"
                )
                return ScriptAnalysisResult(
                    success = false,
                    message = "血缘转换失败: ${lineageResult.errors.joinToString("; ")} (Lineage conversion failed)",
                    scriptId = scriptId
                )
            }

            // 4. 序列化血缘结果为JSON
            val lineageJson = if (lineageResult.lineage != null) {
                serializeLineageResult(lineageResult.lineage)
            } else {
                null
            }

            // 5. 更新数据库
            val finalUpdateResult = updateAnalysisResult(scriptId, lineageJson)

            if (finalUpdateResult) {
                logger.info("c4d1e9a2 | 脚本分析完成: scriptId=$scriptId")
                return ScriptAnalysisResult(
                    success = true,
                    message = "脚本分析完成 (Script analysis completed)",
                    scriptId = scriptId,
                    lineageResult = lineageResult
                )
            } else {
                logger.error("a8b5d2c9 | 保存分析结果失败: scriptId=$scriptId")
                scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.FAILED, "保存分析结果失败")
                return ScriptAnalysisResult(
                    success = false,
                    message = "保存分析结果失败 (Failed to save analysis result)",
                    scriptId = scriptId
                )
            }

        } catch (e: Exception) {
            logger.error("f7b4e1a3 | 脚本分析过程中发生错误: scriptId=$scriptId", e)
            scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.FAILED, "分析失败: ${e.message}")
            return ScriptAnalysisResult(
                success = false,
                message = "分析失败: ${e.message} (Analysis failed)",
                scriptId = scriptId
            )
        }
    }

    /**
     * 获取脚本内容 (Get script content from S3)
     */
    fun getScriptContent(script: UploadedScript): String {
        // 1. 如果 S3 key 存在，优先从 S3 下载
        if (script.filePath != null) {
            val downloadResult = s3StorageService.downloadFile(script.filePath)
            if (downloadResult.success && downloadResult.content != null) {
                return String(downloadResult.content)
            }
            // 如果 S3 下载失败，记录日志并尝试回退
            logger.warn("从S3下载脚本失败 (key: ${script.filePath})，将尝试从数据库获取。错误: ${downloadResult.errorMessage}")
        }

        // 2. S3 获取失败或 key 不存在，从数据库回退
        if (script.scriptContent != null) {
            return script.scriptContent
        }

        // 3. 如果 S3 和数据库中都没有内容，则抛出异常
        throw IllegalStateException("脚本内容在S3和数据库中均未找到 (scriptId: ${script.id})")
    }

    /**
     * 转换脚本为血缘信息 (Convert script to lineage)
     *
     * 类似于HdfsShellScriptLineageConverter的convertToLineage方法
     */
    private fun convertScriptToLineage(script: UploadedScript): ScriptLineageResult {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()

        return try {
            logger.info("d5e2f8a1 | 开始转换脚本血缘: scriptId=${script.id}, scriptType=${script.scriptType}, scriptName=${script.scriptName}")

            val scriptContent = getScriptContent(script)

            // 使用纯函数进行脚本解析
            val parsedLineage = parseScriptToLineage(
                scriptContent = scriptContent,
                scriptType = script.scriptType,
                scriptName = script.scriptName
            )

            if (!parsedLineage.success) {
                return ScriptLineageResult(null, parsedLineage.warnings, parsedLineage.errors, false)
            }

            // 映射解析结果到真实的数据库表
            val mappedLineage = mapParsedLineageToRealTables(script, parsedLineage)

            logger.info("h7k4l9m2 | 成功构建脚本血缘: scriptId=${script.id} (${mappedLineage.lineage?.tableLineage?.sourceTables?.size ?: 0}个源表)")

            mappedLineage

        } catch (e: Exception) {
            logger.error("i3n6o9p2 | 转换脚本血缘时发生异常: scriptId=${script.id}", e)
            errors.add("转换过程中发生异常: ${e.message}")
            ScriptLineageResult(null, warnings, errors, false)
        }
    }


    /**
     * 将解析后的血缘信息映射到真实的数据库表 (Map parsed lineage to real database tables)
     */
    private fun mapParsedLineageToRealTables(
        script: UploadedScript,
        parsedLineage: ParsedScriptLineage
    ): ScriptLineageResult {
        val warnings = parsedLineage.warnings.toMutableList()
        val errors = parsedLineage.errors.toMutableList()

        return try {
            logger.info("928ce22a | 开始映射表到数据库中的真实表")

            // 映射所有表到数据库中的真实表
            val sourceTableInfos: List<TableInfo> = parsedLineage.sourceTables.map { sourceTable ->
                mapToRealTable(sourceTable, "源表")
            }

            val targetTableInfos: List<TableInfo> = parsedLineage.targetTables.map { targetTable ->
                mapToRealTable(targetTable, "目标表")
            }

            // 构建血缘信息
            val lineageResult = buildScriptLineage(
                script = script,
                sourceTables = sourceTableInfos,
                targetTables = targetTableInfos,
                columnMappings = parsedLineage.columnMappings,
                consolidatedSql = parsedLineage.consolidatedSql,
                warnings = warnings,
                errors = errors
            )

            lineageResult

        } catch (e: Exception) {
            logger.error("l7n0p3q6 | 映射表到真实数据库表失败", e)
            errors.add("映射表到真实数据库表失败: ${e.message}")
            ScriptLineageResult(null, warnings, errors, false)
        }
    }

    /**
     * 解析表全名 (Parse table full name)
     *
     * 将"schema.table"格式的字符串解析为schema和table部分
     * 如果没有schema部分，则返回null作为schema
     */
    private fun parseTableFullName(tableFullName: String): Pair<String?, String> {
        val parts = tableFullName.split(".")
        return when (parts.size) {
            1 -> Pair(null, parts[0])
            2 -> Pair(parts[0], parts[1])
            else -> {
                // 处理包含多个点的情况，取最后一个作为表名，前面的作为schema
                val tableName = parts.last()
                val schema = parts.dropLast(1).joinToString(".")
                Pair(schema, tableName)
            }
        }
    }

    /**
     * 更新分析结果到数据库 (Update analysis result to database)
     */
    private fun updateAnalysisResult(scriptId: Long, analysisResultJson: String?): Boolean {
        return try {
            scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.COMPLETED, analysisResultJson)
        } catch (e: Exception) {
            logger.error("n8o1p4q7 | 更新分析结果到数据库失败: scriptId=$scriptId", e)
            false
        }
    }

    /**
     * 构建脚本血缘 (Build script lineage)
     */
    private fun buildScriptLineage(
        script: UploadedScript,
        sourceTables: List<TableInfo>,
        targetTables: List<TableInfo>,
        columnMappings: List<ColumnMapping>,
        consolidatedSql: String,
        warnings: MutableList<String>,
        errors: MutableList<String>
    ): ScriptLineageResult {
        return try {

            // 如果有目标表，创建完整的血缘关系
            if (targetTables.isNotEmpty()) {
                val targetTableInfo = targetTables.first()

                val tableLineage = TableLineage(
                    sourceTables = sourceTables,
                    targetTable = targetTableInfo,
                    lineageType = LineageType.DIRECT_COPY
                )

                val columnLineages = columnMappings.mapIndexed { index, mapping ->
                    ColumnLineage(
                        sourceColumn = ColumnInfo(
                            columnName = mapping.sourceColumn.name,
                            dataType = "unknown",
                            comment = null,
                            table = findSourceTableForColumn(sourceTables, mapping, targetTableInfo)
                        ),
                        targetColumn = ColumnInfo(
                            columnName = mapping.targetColumnName ?: mapping.sourceColumn.name,
                            dataType = "unknown",
                            comment = null,
                            table = targetTableInfo
                        ),
                        transformation = null,
                        columnIndex = index
                    )
                }

                // 使用目标表的数据库信息
                val sourceDatabase = sourceTables.firstOrNull()?.database ?: targetTableInfo.database
                val targetDatabase = targetTableInfo.database

                val dataLineage = DataLineage(
                    jobId = "script_${script.id}",
                    jobName = script.scriptName,
                    tableLineage = tableLineage,
                    columnLineages = columnLineages,
                    sourceDatabase = sourceDatabase,
                    targetDatabase = targetDatabase,
                    originalSql = consolidatedSql
                )

                ScriptLineageResult(dataLineage, warnings, errors, true)
            } else {
                // 只有源表，创建简化的血缘关系
                warnings.add("脚本中只包含查询语句，未发现数据写入操作")
                ScriptLineageResult(null, warnings, errors, true)
            }
        } catch (e: Exception) {
            logger.error("l7n0p3q6 | 构建脚本血缘失败", e)
            errors.add("构建血缘关系失败: ${e.message}")
            ScriptLineageResult(null, warnings, errors, false)
        }
    }

    /**
     * 映射SQL表引用到数据库中的真实表 (Map SQL table reference to real table in database)
     */
    private fun mapToRealTable(sqlTable: SqlTableReference, tableType: String): TableInfo {
        val tableFullName = if (sqlTable.schemaOrDatabase != null) {
            "${sqlTable.schemaOrDatabase}.${sqlTable.name}"
        } else {
            sqlTable.name
        }

        logger.debug("e5f6g7h8 | 映射${tableType}: $tableFullName")

        // 查找表在lineage_tables中的记录
        val tableRecord = if (sqlTable.schemaOrDatabase != null) {
            findTableRecordBySchemaOrDatabase(sqlTable.name, sqlTable.schemaOrDatabase)
        } else {
            findTableRecordByTableNameOnly(sqlTable.name)
        } ?: throw ScriptAnalysisException("${tableType}未在数据库中找到: $tableFullName")

        // 根据datasource_id获取数据源信息
        val datasource = findDatasourceById(tableRecord.datasourceId)
            ?: throw ScriptAnalysisException("${tableType}对应的数据源未找到: datasourceId=${tableRecord.datasourceId}, table=$tableFullName")

        logger.info("i9j0k1l2 | 成功映射${tableType}: $tableFullName -> datasource=${datasource.datasourceName}")

        // 根据数据源类型正确解释schema_name字段的含义
        val actualSchema = determineActualSchema(datasource.dbType, sqlTable.schemaOrDatabase, tableRecord.schemaName)

        return TableInfo(
            schema = actualSchema,
            tableName = tableRecord.tableName,
            database = DatabaseInfo(
                dbType = datasource.dbType,
                host = datasource.host,
                port = datasource.port,
                databaseName = datasource.databaseName,
                originalConnectionString = datasource.connectionString
            ),
            id = tableRecord.id,
            alias = sqlTable.alias,
        )
    }

    /**
     * 根据列映射信息找到对应的源表
     * 优先匹配表名或别名，如果没有匹配则返回第一个源表或目标表作为兜底
     */
    private fun findSourceTableForColumn(
        sourceTables: List<TableInfo>,
        columnMapping: ColumnMapping,
        targetTableInfo: TableInfo
    ): TableInfo {
        val find = sourceTables.find { table ->
            table.tableName == columnMapping.sourceColumn.tablePrefix ||
                    table.alias == columnMapping.sourceColumn.tablePrefix
        }
        return find ?: sourceTables.firstOrNull() ?: targetTableInfo
    }

    /**
     * 根据数据库类型确定实际的schema值
     * 默认情况下，a.b中的a被视为数据库名，不是schema
     * PostgreSQL和Oracle明确支持schema概念时才保留作为schema
     */
    private fun determineActualSchema(
        dbType: String,
        parsedSchemaOrDatabase: String?,
        storedSchemaName: String?
    ): String? {
        logger.info("ac6b128e | dbType = $dbType ; parsedSchemaOrDatabase = $parsedSchemaOrDatabase ; storedSchemaName = $storedSchemaName")
        return when (dbType.lowercase()) {
            "postgresql", "oracle" -> {
                // PostgreSQL和Oracle明确支持schema概念，保持原有逻辑
                storedSchemaName
            }

            else -> {
                // 其他所有数据库类型（Hive/MySQL等），默认将a.b中的a视为数据库名
                if (parsedSchemaOrDatabase != null && storedSchemaName == parsedSchemaOrDatabase) {
                    logger.debug("b2c3d4e5 | 对于${dbType}数据库，将数据库名'$parsedSchemaOrDatabase'识别为schema=null")
                    null
                } else {
                    // 如果不同，可能是真正的schema（但这种情况很少见）
                    storedSchemaName
                }
            }
        }
    }

    /**
     * 根据表名查找表记录，仅匹配表名 (Find table record by table name only)
     */
    private fun findTableRecordByTableNameOnly(tableName: String): LineageTableRecord? {
        return try {
            val sql = """
                SELECT lt.id, lt.datasource_id, lt.schema_name, lt.table_name 
                FROM lineage_tables lt
                WHERE lt.table_name = ? AND lt.status = 'ACTIVE'
                ORDER BY lt.created_at ASC
            """.trimIndent()

            val results = jdbcTemplate.query(sql, { rs, _ ->
                LineageTableRecord(
                    id = rs.getLong("id"),
                    datasourceId = rs.getLong("datasource_id"),
                    schemaName = rs.getString("schema_name"),
                    tableName = rs.getString("table_name")
                )
            }, tableName)

            when {
                results.isEmpty() -> {
                    logger.debug("m3n4o5p6 | 未找到表记录: tableName=$tableName")
                    null
                }

                results.size > 1 -> {
                    logger.warn("q7r8s9t0 | 发现多个匹配的表记录(${results.size}个)，使用最早创建的记录: tableName=$tableName")
                    results[0]
                }

                else -> {
                    logger.debug("u1v2w3x4 | 找到表记录: tableName=$tableName, id=${results[0].id}")
                    results[0]
                }
            }
        } catch (e: Exception) {
            logger.error("y5z6a7b8 | 查询表记录时发生错误: tableName=$tableName", e)
            null
        }
    }

    /**
     * 根据表名和模式名/数据库名查找表记录 (Find table record by table name and schema/database name)
     * 先尝试匹配schema_name，如果找不到则尝试匹配database_name
     */
    private fun findTableRecordBySchemaOrDatabase(tableName: String, schemaOrDbName: String): LineageTableRecord? {
        return try {
            logger.debug("a2b3c4d5 | 查找表记录: tableName=$tableName, schemaOrDbName=$schemaOrDbName")

            // 1. 首先尝试按schema_name匹配
            val schemaMatchSql = """
                SELECT lt.id, lt.datasource_id, lt.schema_name, lt.table_name 
                FROM lineage_tables lt
                WHERE lt.table_name = ? AND lt.schema_name = ? AND lt.status = 'ACTIVE'
                ORDER BY lt.created_at ASC
            """.trimIndent()

            val schemaResults = jdbcTemplate.query(schemaMatchSql, { rs, _ ->
                LineageTableRecord(
                    id = rs.getLong("id"),
                    datasourceId = rs.getLong("datasource_id"),
                    schemaName = rs.getString("schema_name"),
                    tableName = rs.getString("table_name")
                )
            }, tableName, schemaOrDbName)

            if (schemaResults.isNotEmpty()) {
                val result = schemaResults[0]
                if (schemaResults.size > 1) {
                    logger.warn("e6f7g8h9 | 通过schema_name找到多个匹配记录(${schemaResults.size}个)，使用最早创建的记录: tableName=$tableName, schema=$schemaOrDbName")
                }
                logger.info("i0j1k2l3 | 通过schema_name找到表记录: tableName=$tableName, schema=$schemaOrDbName, id=${result.id}")
                return result
            }

            // 2. 如果schema_name没有匹配，尝试按database_name匹配
            val databaseMatchSql = """
                SELECT lt.id, lt.datasource_id, lt.schema_name, lt.table_name 
                FROM lineage_tables lt
                JOIN lineage_datasources ld ON lt.datasource_id = ld.id
                WHERE lt.table_name = ? AND ld.database_name = ? AND lt.status = 'ACTIVE' AND ld.status = 'ACTIVE'
                ORDER BY lt.created_at ASC
            """.trimIndent()

            val databaseResults = jdbcTemplate.query(databaseMatchSql, { rs, _ ->
                LineageTableRecord(
                    id = rs.getLong("id"),
                    datasourceId = rs.getLong("datasource_id"),
                    schemaName = rs.getString("schema_name"),
                    tableName = rs.getString("table_name")
                )
            }, tableName, schemaOrDbName)

            when {
                databaseResults.isEmpty() -> {
                    logger.info("m4n5o6p7 | 未找到表记录: tableName=$tableName, schemaOrDbName=$schemaOrDbName")
                    null
                }

                databaseResults.size > 1 -> {
                    logger.warn("q8r9s0t1 | 通过database_name找到多个匹配记录(${databaseResults.size}个)，使用最早创建的记录: tableName=$tableName, database=$schemaOrDbName")
                    databaseResults[0]
                }

                else -> {
                    val result = databaseResults[0]
                    logger.debug("u2v3w4x5 | 通过database_name找到表记录: tableName=$tableName, database=$schemaOrDbName, id=${result.id}")
                    result
                }
            }
        } catch (e: Exception) {
            logger.error("y6z7a8b9 | 查询表记录时发生错误: tableName=$tableName, schemaOrDbName=$schemaOrDbName", e)
            null
        }
    }

    /**
     * 根据ID查找数据源 (Find datasource by ID)
     */
    private fun findDatasourceById(datasourceId: Long): LineageDatasourceRecord? {
        return try {
            val sql = """
                SELECT id, datasource_name, db_type, host, port, database_name, connection_string
                FROM lineage_datasources 
                WHERE id = ? AND status = 'ACTIVE'
            """.trimIndent()

            val results = jdbcTemplate.query(sql, { rs, _ ->
                LineageDatasourceRecord(
                    id = rs.getLong("id"),
                    datasourceName = rs.getString("datasource_name"),
                    dbType = rs.getString("db_type"),
                    host = rs.getString("host"),
                    port = rs.getInt("port"),
                    databaseName = rs.getString("database_name"),
                    connectionString = rs.getString("connection_string")
                )
            }, datasourceId)

            if (results.isEmpty()) {
                logger.warn("c9d0e1f2 | 未找到数据源: datasourceId=$datasourceId")
                null
            } else {
                logger.debug("g3h4i5j6 | 找到数据源: datasourceId=$datasourceId, name=${results[0].datasourceName}")
                results[0]
            }
        } catch (e: Exception) {
            logger.error("k7l8m9n0 | 查询数据源时发生错误: datasourceId=$datasourceId", e)
            null
        }
    }

    /**
     * 序列化血缘结果为JSON (Serialize lineage result to JSON)
     */
    private fun serializeLineageResult(dataLineage: DataLineage): String {
        return try {
            objectMapper.writeValueAsString(dataLineage)
        } catch (e: Exception) {
            logger.error("m9o2p5q8 | 序列化血缘结果失败", e)
            throw ScriptAnalysisException("序列化血缘结果失败: ${e.message}", e)
        }
    }

    companion object {
        /**
         * 纯函数：解析脚本内容为血缘信息 (Pure function: Parse script content to lineage)
         *
         * 这是一个纯函数，不依赖任何外部状态，方便单元测试
         */
        fun parseScriptToLineage(
            scriptContent: String,
            scriptType: ScriptType,
            scriptName: String
        ): ParsedScriptLineage {
            val warnings = mutableListOf<String>()
            val errors = mutableListOf<String>()

            return try {
                // 1. 从脚本中提取SQL语句
                val extractedSqls = when (scriptType) {
                    ScriptType.SQL -> extractSqlFromSqlScript(scriptContent, warnings)
                    ScriptType.SHELL -> extractSqlFromShellScript(scriptContent, warnings)
                }

                if (extractedSqls.isEmpty()) {
                    warnings.add("未从脚本中提取到任何SQL语句")
                    return ParsedScriptLineage(
                        sourceTables = emptyList(),
                        targetTables = emptyList(),
                        columnMappings = emptyList(),
                        consolidatedSql = "",
                        warnings = warnings,
                        errors = errors,
                        success = false
                    )
                }

                // 2. 应用SQL预处理和过滤规则
                val processedSqls = extractedSqls.mapNotNull { sql ->
                    HdfsShellScriptLineageConverter.processRawSqlStatement(sql, warnings)
                }

                if (processedSqls.isEmpty()) {
                    warnings.add("所有SQL语句在预处理后被过滤，无法进行血缘分析")
                    return ParsedScriptLineage(
                        sourceTables = emptyList(),
                        targetTables = emptyList(),
                        columnMappings = emptyList(),
                        consolidatedSql = "",
                        warnings = warnings,
                        errors = errors,
                        success = false
                    )
                }

                // 3. 解析每个SQL语句获取血缘信息
                val allSourceTables = mutableSetOf<SqlTableReference>()
                val allTargetTables = mutableSetOf<SqlTableReference>()
                val allColumnMappings = mutableListOf<ColumnMapping>()
                val consolidatedSql = processedSqls.joinToString("\n-- Next SQL --\n")

                for ((index, sql) in processedSqls.withIndex()) {
                    try {
                        val parseResult: SqlParseResult = parseSqlStatement(sql)
                        if (parseResult.isDataModification) {
                            // 数据修改语句：分别处理源表和目标表
                            allSourceTables.addAll(parseResult.sourceTables)
                            parseResult.targetTable?.let { allTargetTables.add(it) }
                            allColumnMappings.addAll(parseResult.columnMappings)
                        } else {
                            // 查询语句：所有表都是源表
                            allSourceTables.addAll(parseResult.tables)
                            // 对于SELECT语句，创建简单的列映射
                            parseResult.columns.forEachIndexed { index, column ->
                                allColumnMappings.add(ColumnMapping(column, column.name, index))
                            }
                        }
                    } catch (e: SqlParsingException) {
                        warnings.add("SQL解析失败 (SQL ${index + 1}): ${e.message}")
                    }
                }

                if (allSourceTables.isEmpty() && allTargetTables.isEmpty()) {
                    warnings.add("所有SQL解析均失败，无法提取表血缘信息")
                    return ParsedScriptLineage(
                        sourceTables = emptyList(),
                        targetTables = emptyList(),
                        columnMappings = emptyList(),
                        consolidatedSql = consolidatedSql,
                        warnings = warnings,
                        errors = errors,
                        success = false
                    )
                }

                ParsedScriptLineage(
                    sourceTables = allSourceTables.toList(),
                    targetTables = allTargetTables.toList(),
                    columnMappings = allColumnMappings,
                    consolidatedSql = consolidatedSql,
                    warnings = warnings,
                    errors = errors,
                    success = true
                )

            } catch (e: Exception) {
                errors.add("解析过程中发生异常: ${e.message}")
                ParsedScriptLineage(
                    sourceTables = emptyList(),
                    targetTables = emptyList(),
                    columnMappings = emptyList(),
                    consolidatedSql = "",
                    warnings = warnings,
                    errors = errors,
                    success = false
                )
            }
        }

        /**
         * 从 SQL 脚本中提取 SQL 语句 (Extract SQL from SQL script)
         */
        private fun extractSqlFromSqlScript(scriptContent: String, warnings: MutableList<String>): List<String> {
            return try {
                // 按分号分隔SQL语句
                val sqlStatements = scriptContent.split(";")
                    .map { it.trim() }
                    .filter { it.isNotBlank() && !it.startsWith("--") }

                sqlStatements
            } catch (e: Exception) {
                warnings.add("从SQL脚本提取SQL失败: ${e.message}")
                emptyList()
            }
        }

        /**
         * 从 Shell 脚本中提取 SQL 语句 (Extract SQL from Shell script)
         */
        private fun extractSqlFromShellScript(scriptContent: String, warnings: MutableList<String>): List<String> {
            return try {
                ShellScriptParser.parseShellScriptForSql(scriptContent)
            } catch (e: Exception) {
                warnings.add("从Shell脚本提取SQL失败: ${e.message}")
                emptyList()
            }
        }

        /**
         * 解析SQL语句 (Parse SQL statement)
         */
        private fun parseSqlStatement(sql: String): SqlParseResult {
            val dataModResult: DataModificationResult = SqlParser.parseDataModification(sql)
            return SqlParseResult(
                tables = dataModResult.sourceTables,
                columns = dataModResult.sourceColumns,
                isDataModification = true,
                sourceTables = dataModResult.sourceTables,
                targetTable = dataModResult.targetTable,
                columnMappings = dataModResult.columnMappings
            )
        }
    }
}

/**
 * SQL解析结果 (SQL Parse Result)
 */
data class SqlParseResult(
    val tables: List<SqlTableReference>,
    val columns: List<SqlColumnReference>,
    val isDataModification: Boolean,
    val sourceTables: List<SqlTableReference> = emptyList(),
    val targetTable: SqlTableReference? = null,
    val columnMappings: List<ColumnMapping> = emptyList()
)

/**
 * 解析后的脚本血缘信息 (Parsed Script Lineage)
 *
 * 纯函数解析的中间结果，包含从SQL中提取的表引用和列映射
 */
data class ParsedScriptLineage(
    val sourceTables: List<SqlTableReference>,
    val targetTables: List<SqlTableReference>,
    val columnMappings: List<ColumnMapping>,
    val consolidatedSql: String,
    val warnings: List<String>,
    val errors: List<String>,
    val success: Boolean
)

/**
 * 脚本血缘结果 (Script Lineage Result)
 */
data class ScriptLineageResult(
    val lineage: DataLineage?,
    val warnings: List<String>,
    val errors: List<String>,
    val success: Boolean
)

/**
 * 脚本分析结果 (Script Analysis Result)
 *
 * 分析操作的执行结果
 */
data class ScriptAnalysisResult(
    val success: Boolean,
    val message: String,
    val scriptId: Long,
    val lineageResult: ScriptLineageResult? = null
)

/**
 * 脚本影响分析结果 (Script Impact Analysis Result)
 *
 * 临时兼容性数据结构，用于与现有API兼容
 */
data class ScriptImpactAnalysisResult(
    val scriptId: Long?,
    val analyzedAt: LocalDateTime,
    val directRelationships: List<DirectTableRelationship>,
    val extendedLineage: List<LineageData>,
    val summary: ScriptAnalysisSummary
)

/**
 * 直接表关系 (Direct Table Relationship)
 */
data class DirectTableRelationship(
    val schema: String?,
    val tableName: String,
    val alias: String?,
    val statementType: String,
    val statementIndex: Int,
    val isSource: Boolean,
    val isTarget: Boolean,
    val hasLineageData: Boolean
)

/**
 * 血缘数据 (Lineage Data)
 */
data class LineageData(
    val tableId: Long?,
    val tableFullName: String,
    val upstreamTables: List<TableLineageView>,
    val downstreamTables: List<TableLineageView>
)

/**
 * 脚本分析摘要 (Script Analysis Summary)
 */
data class ScriptAnalysisSummary(
    val totalTablesFound: Int,
    val uniqueTablesFound: Int,
    val tablesWithLineageData: Int,
    val totalUpstreamCount: Int,
    val totalDownstreamCount: Int
)

/**
 * 脚本分析异常 (Script Analysis Exception)
 */
class ScriptAnalysisException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

/**
 * 血缘表记录 (Lineage Table Record)
 */
data class LineageTableRecord(
    val id: Long,
    val datasourceId: Long,
    val schemaName: String?,
    val tableName: String
)

/**
 * 血缘数据源记录 (Lineage Datasource Record)
 */
data class LineageDatasourceRecord(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val connectionString: String
)